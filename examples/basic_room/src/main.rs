use livekit::prelude::*;
use livekit_api::access_token;
use std::env;
use std::process::Command;
use std::sync::Arc;
use tokio::sync::Mutex;
use libwebrtc::audio_stream::native::NativeAudioStream;

// Connect to a room using the specified env variables
// and print all incoming events, with audio playback support

#[tokio::main]
async fn main() {
    env_logger::init();

    // let url = "wss://cai-release-ehw1ezzy.livekit.cloud";
    // let api_key = "API2Z4aF3Gs4KJ7";
    // let api_secret = "hVdcnfJmocyd0u8wP2iaftRodZVQPXitBHdXbGFgSOA";
    let url = "wss://my-test-application-sqf51mcb.livekit.cloud";
    let api_key = "APIuZith5Gv5Jd6";
    let api_secret = "PudiWJXZxWf3jHjlcLCCzu9vs5EGDun1DQjgiDFI3tR";

    let token = access_token::AccessToken::with_api_key(&api_key, &api_secret)
        .with_identity("rust-bot")
        .with_name("Rust Bot")
        .with_grants(access_token::VideoGrants {
            room_join: true,
            room: "my-room1".to_string(),
            ..Default::default()
        })
        .to_jwt()
        .unwrap();

    let (room, mut rx) = Room::connect(&url, &token, RoomOptions::default()).await.unwrap();
    log::info!("Connected to room: {} - {}", room.name(), String::from(room.sid().await));

    room.local_participant()
        .publish_data(DataPacket {
            payload: "Hello world".to_owned().into_bytes(),
            reliable: true,
            ..Default::default()
        })
        .await
        .unwrap();

    // Store active audio players
    let audio_players: Arc<Mutex<Vec<tokio::task::JoinHandle<()>>>> = Arc::new(Mutex::new(Vec::new()));

    while let Some(msg) = rx.recv().await {
        log::info!("Event: {:?}", msg);

        match msg {
            RoomEvent::TrackSubscribed { track, publication: _, participant } => {
                if let RemoteTrack::Audio(audio_track) = track {
                    log::info!("Audio track subscribed from participant: {}", participant.identity());

                    // Start playing the audio track
                    let players = audio_players.clone();
                    let handle = tokio::spawn(async move {
                        if let Err(e) = play_audio_track(audio_track).await {
                            log::error!("Error playing audio track: {}", e);
                        }
                    });

                    players.lock().await.push(handle);
                }
            }
            _ => {}
        }
    }
}

async fn play_audio_track(audio_track: RemoteAudioTrack) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    use futures_util::StreamExt;

    log::info!("Starting audio playback for track: {}", audio_track.name());

    // Create audio stream to receive audio frames
    let sample_rate = 48000; // Standard sample rate
    let num_channels = 2;    // Stereo
    let mut audio_stream = NativeAudioStream::new(audio_track.rtc_track(), sample_rate, num_channels);

    // Start aplay process to play audio - explicitly use headphone jack (card 2)
    let mut aplay = Command::new("aplay")
        .args(&[
            "-D", "hw:2,0",           // Use card 2 (headphones), device 0
            "-f", "S16_LE",           // 16-bit signed little endian
            "-r", &sample_rate.to_string(), // Sample rate
            "-c", &num_channels.to_string(), // Number of channels
            "-t", "raw",              // Raw audio format
            "-",                      // Read from stdin
        ])
        .stdin(std::process::Stdio::piped())
        .stdout(std::process::Stdio::null())
        .stderr(std::process::Stdio::null())
        .spawn()?;

    let stdin = aplay.stdin.take().ok_or("Failed to get aplay stdin")?;
    let mut stdin = tokio::process::ChildStdin::from_std(stdin)?;

    use tokio::io::AsyncWriteExt;

    // Stream audio frames and write to aplay
    while let Some(frame) = audio_stream.next().await {
        // Convert audio frame data to bytes
        let audio_bytes: Vec<u8> = frame.data
            .iter()
            .flat_map(|&sample| sample.to_le_bytes())
            .collect();

        if let Err(e) = stdin.write_all(&audio_bytes).await {
            log::warn!("Failed to write audio data: {}", e);
            break;
        }
    }

    // Clean up
    let _ = stdin.shutdown().await;
    let _ = aplay.wait();

    log::info!("Audio playback ended for track");
    Ok(())
}
